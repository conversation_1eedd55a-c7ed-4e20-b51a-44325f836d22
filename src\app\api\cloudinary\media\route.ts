/**
 * Cloudinary Media API Route
 *
 * Handles fetching media items from Supabase database with Cloudinary sync.
 * Provides fast, persistent access to media library with perfect mirroring.
 */

import { NextRequest, NextResponse } from 'next/server'
import { SupabaseMediaService } from '@/lib/supabaseMediaService'
import { BidirectionalSyncService } from '@/lib/bidirectionalSyncService'

/**
 * GET /api/cloudinary/media
 * Fetch media items with enterprise-grade pagination and filtering
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[Cloudinary Media API] Fetching media items...')

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const folder = searchParams.get('folder') || undefined
    const resourceType = searchParams.get('resource_type') as 'image' | 'video' | 'raw' | undefined
    const search = searchParams.get('search') || undefined
    const tags = searchParams.get('tags')?.split(',').filter(Boolean) || undefined
    const sortBy = searchParams.get('sort_by')?.split(',') || undefined
    const sortOrder = searchParams.get('sort_order') as 'asc' | 'desc' || 'desc'


    // First, check if database is properly set up
    let databaseReady = false
    let result: {
      assets: Array<{
        public_id: string;
        secure_url: string;
        format: string;
        bytes: number;
        width?: number;
        height?: number;
        resource_type: string;
        created_at: string;
        tags: string[];
        [key: string]: unknown;
      }>;
      total: number;
      page: number;
      limit: number;
      has_next: boolean;
      has_prev: boolean;
    } | null = null
    let stats: {
      total_assets: number;
      total_images: number;
      total_videos: number;
      total_size: number;
      synced_assets: number;
      pending_assets: number;
      error_assets: number;
    } | null = null

    try {
      // Tables exist, so test actual functionality
      databaseReady = true

      // If database is ready, fetch actual data
      result = await SupabaseMediaService.searchMediaAssets({
        page,
        limit,
        folder,
        resource_type: resourceType,
        search,
        tags,
        sort_by: sortBy?.[0] || 'created_at',
        sort_order: sortOrder
      })

      stats = await SupabaseMediaService.getMediaStats()

      console.log(`[Cloudinary Media API] Found ${result.assets.length} items (page ${page}) from database`)

    } catch (dbError) {
      console.warn('[Cloudinary Media API] Database not ready:', dbError)

      // Check if this is a table missing error
      const errorMessage = dbError instanceof Error ? dbError.message : String(dbError)
      const isTableMissing = errorMessage.includes('relation "media_assets" does not exist') ||
                            errorMessage.includes('table "media_assets" does not exist') ||
                            errorMessage.includes('does not exist') ||
                            errorMessage.includes('Failed to search media assets')

      databaseReady = !isTableMissing

      // Fallback: Return empty result with setup instructions
      result = {
        assets: [],
        total: 0,
        page: 1,
        limit: 50,
        has_next: false,
        has_prev: false
      }

      stats = {
        total_assets: 0,
        total_images: 0,
        total_videos: 0,
        total_size: 0,
        synced_assets: 0,
        pending_assets: 0,
        error_assets: 0
      }
    }

    return NextResponse.json({
      success: databaseReady,
      items: result.assets,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        pages: Math.ceil(result.total / result.limit),
        has_next: result.has_next,
        has_prev: result.has_prev
      },
      stats: {
        total_images: stats.total_images,
        total_videos: stats.total_videos,
        total_files: stats.total_assets,
        total_size: stats.total_size
      },
      sync_status: {
        last_sync: new Date().toISOString(),
        is_synced: databaseReady && stats.pending_assets === 0 && stats.error_assets === 0,
        pending_operations: stats.pending_assets + stats.error_assets,
        synced_assets: stats.synced_assets,
        error_assets: stats.error_assets,
        database_ready: databaseReady
      },
      database_setup: {
        ready: databaseReady,
        message: databaseReady
          ? 'Database is properly configured'
          : 'Database setup required - run the SQL script from docs/full-complete-supabase-script.md',
        setup_endpoint: '/api/setup-media-db',
        script_location: 'docs/full-complete-supabase-script.md'
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Cloudinary Media API] Fetch failed:', error)

    // Return empty result with error information
    return NextResponse.json({
      success: false,
      items: [],
      pagination: {
        page: 1,
        limit: 50,
        total: 0,
        pages: 0,
        has_next: false,
        has_prev: false
      },
      stats: {
        total_images: 0,
        total_videos: 0,
        total_files: 0,
        total_size: 0
      },
      sync_status: {
        last_sync: new Date().toISOString(),
        is_synced: false,
        pending_operations: 0,
        database_ready: false
      },
      database_setup: {
        ready: false,
        message: 'Database setup required - run the SQL script from docs/full-complete-supabase-script.md',
        setup_endpoint: '/api/setup-media-db',
        script_location: 'docs/full-complete-supabase-script.md'
      },
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * DELETE /api/cloudinary/media
 * Delete media items with bidirectional sync
 */
export async function DELETE(request: NextRequest) {
  try {
    console.log('[Cloudinary Media API] Processing delete request...')

    // Parse query parameters for public IDs
    const { searchParams } = new URL(request.url)
    const publicIds = searchParams.get('public_ids')?.split(',').filter(Boolean) || []

    if (publicIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No public IDs provided for deletion',
        timestamp: new Date().toISOString()
      }, { status: 400 })
    }

    console.log(`[Cloudinary Media API] Deleting ${publicIds.length} items:`, publicIds)

    const results = {
      deleted: [] as string[],
      failed: [] as { public_id: string, error: string }[]
    }

    // Process each deletion
    for (const publicId of publicIds) {
      try {
        // Get asset info first
        const asset = await SupabaseMediaService.getMediaAssetByPublicId(publicId)

        if (!asset) {
          results.failed.push({ public_id: publicId, error: 'Asset not found in database' })
          continue
        }

        // Soft delete in database (will be synced to Cloudinary)
        await SupabaseMediaService.softDeleteMediaAsset(publicId, 'admin')

        // Mark as pending for Cloudinary sync
        await SupabaseMediaService.updateSyncStatus(publicId, 'pending')

        results.deleted.push(publicId)

        console.log(`[Cloudinary Media API] Marked for deletion: ${publicId}`)

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error'
        results.failed.push({ public_id: publicId, error: errorMsg })
        console.error(`[Cloudinary Media API] Failed to delete ${publicId}:`, error)
      }
    }

    // Trigger background sync to delete from Cloudinary
    // Note: In production, this should be handled by a background job or webhook
    try {
      await BidirectionalSyncService.performFullSync({ batch_size: 50 })
    } catch (syncError) {
      console.warn('[Cloudinary Media API] Background sync failed:', syncError)
      // Don't fail the request if sync fails - it will be retried
    }

    return NextResponse.json({
      success: results.failed.length === 0,
      message: `Deleted ${results.deleted.length} items, ${results.failed.length} failed`,
      deleted: results.deleted,
      failed: results.failed,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Cloudinary Media API] Delete operation failed:', error)

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Delete operation failed',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}


